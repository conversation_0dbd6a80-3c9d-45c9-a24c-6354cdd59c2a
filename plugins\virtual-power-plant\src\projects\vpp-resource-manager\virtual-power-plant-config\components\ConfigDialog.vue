<template>
  <el-dialog
    :title="$T('配置虚拟电厂')"
    :visible.sync="dialogVisible"
    width="880px"
    @close="handleClose"
    class="config-dialog"
    append-to-body
  >
    <div class="dialog-content">
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item :label="$T('虚拟电厂名称')" prop="vppName">
              <el-input
                v-model="form.vppName"
                :placeholder="$T('请输入内容')"
                class="custom-input"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$T('虚拟电厂所属省份')" prop="province">
              <el-select
                v-model="form.province"
                :placeholder="$T('请选择省份')"
                class="custom-input"
                @change="handleProvinceChange"
              >
                <el-option
                  v-for="province in provinceOptions"
                  :key="province.code"
                  :label="province.name"
                  :value="province.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$T('虚拟电厂场站类型')" prop="vppTypeArray">
              <el-select
                v-model="form.vppTypeArray"
                :placeholder="$T('请选择类型')"
                class="custom-input"
                multiple
                collapse-tags
                @change="handleVppTypeChange"
              >
                <el-option
                  v-for="option in vppTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item :label="$T('虚拟电厂成立日期')" prop="createTime">
              <el-date-picker
                v-model="form.createTime"
                type="date"
                value-format="timestamp"
                :placeholder="$T('请选择日期')"
                class="custom-input"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$T('运营商编号')" prop="operatorcode">
              <el-input
                v-model="form.operatorcode"
                :placeholder="$T('请输入内容')"
                class="custom-input"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="$T('虚拟电厂描述')">
          <el-input
            type="textarea"
            :rows="3"
            v-model="form.description"
            :placeholder="$T('请输入虚拟电厂描述')"
            class="station-desc-input"
          ></el-input>
        </el-form-item>
        <el-form-item :label="$T('上传图片')">
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="handleImageUpload"
            accept="image/png,image/jpg,image/jpeg"
          >
            <div v-if="form.imageUrl" class="image-preview">
              <FileImage
                fit="contain"
                style="height: 150px; width: 200px"
                :source="form.imageUrl"
                :placeholderImg="defaultImage"
                :onHandleImgSrc="
                  fileName =>
                    `/vpp/api/v1/resource-manager/base-config/images/download/${fileName}`
                "
                hideNotice
              />
              <div class="image-actions">
                <i class="el-icon-delete" @click.stop="removeImage"></i>
              </div>
            </div>
            <div v-else class="upload-placeholder">
              <i class="el-icon-plus"></i>
            </div>
          </el-upload>
          <div class="upload-tip text-T3 text-sm mt-J1">
            {{ $T("请上传PNG、JPG、JPEG文件，大小在1M以内") }}
          </div>
        </el-form-item>

        <!-- 需求响应 -->
        <h4>{{ $T("需求响应") }}</h4>
        <el-row :gutter="16">
          <el-col :span="10">
            <el-form-item
              :label="$T('申报价格上限')"
              prop="demandResponseDeclaredPriceCaps"
            >
              <el-input
                v-model="form.demandResponseDeclaredPriceCaps"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MWh") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item
              :label="$T('申报价格下限')"
              prop="demandResponseFilingPriceCaps"
            >
              <el-input
                v-model="form.demandResponseFilingPriceCaps"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MWh") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 调峰 -->
        <h4>{{ $T("调峰") }}</h4>
        <el-row :gutter="16">
          <el-col :span="10">
            <el-form-item
              :label="$T('申报价格上限')"
              prop="peakingDeclaredPriceFloor"
            >
              <el-input
                v-model="form.peakingDeclaredPriceFloor"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MWh") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item
              :label="$T('申报价格下限')"
              prop="peakingFilingPriceFloor"
            >
              <el-input
                v-model="form.peakingFilingPriceFloor"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MWh") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 调频 -->
        <h4>{{ $T("调频") }}</h4>
        <el-row :gutter="16">
          <el-col :span="10">
            <el-form-item
              :label="$T('申报价格上限')"
              prop="pmDeclaredPriceFloor"
            >
              <el-input
                v-model="form.pmDeclaredPriceFloor"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MW") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item :label="$T('申报价格下限')" prop="pmFilingPriceFloor">
              <el-input
                v-model="form.pmFilingPriceFloor"
                :placeholder="$T('请输入数值')"
                class="price-input"
              >
                <template slot="append">{{ $T("元/MW") }}</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 调节 - 仅当类型包含调节型时显示 -->
        <div v-if="form.vppType && form.vppType.toString().includes('3')">
          <h4>{{ $T("调节") }}</h4>
          <el-row :gutter="16">
            <el-col :span="10">
              <el-form-item
                :label="$T('申报价格上限')"
                prop="adjustDeclaredPriceFloor"
              >
                <el-input
                  v-model="form.adjustDeclaredPriceFloor"
                  :placeholder="$T('请输入数值')"
                  class="price-input"
                >
                  <template slot="append">{{ $T("元/MWh") }}</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item
                :label="$T('申报价格下限')"
                prop="adjustFilingPriceFloor"
              >
                <el-input
                  v-model="form.adjustFilingPriceFloor"
                  :placeholder="$T('请输入数值')"
                  class="price-input"
                >
                  <template slot="append">{{ $T("元/MWh") }}</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 能量 - 仅当类型包含能量型时显示 -->
        <div v-if="form.vppType && form.vppType.toString().includes('4')">
          <h4>{{ $T("能量") }}</h4>
          <el-row :gutter="16">
            <el-col :span="10">
              <el-form-item
                :label="$T('申报价格上限')"
                prop="energyResponseDeclaredPriceFloor"
              >
                <el-input
                  v-model="form.energyResponseDeclaredPriceFloor"
                  :placeholder="$T('请输入数值')"
                  class="price-input"
                >
                  <template slot="append">{{ $T("元/MWh") }}</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item
                :label="$T('申报价格下限')"
                prop="energyResponseFilingPriceFloor"
              >
                <el-input
                  v-model="form.energyResponseFilingPriceFloor"
                  :placeholder="$T('请输入数值')"
                  class="price-input"
                >
                  <template slot="append">{{ $T("元/MWh") }}</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">{{ $T("取消") }}</el-button>
      <el-button type="primary" @click="handleConfirm">
        {{ $T("确定") }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
import { uploadImage, getPowerplantTypesByProvince } from "@/api/base-config";
import FileImage from "./FileImage.vue";
import { getEnumOptions } from "@/utils/enumManager";

export default {
  name: "ConfigDialog",
  components: {
    FileImage
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    vppData: {
      type: Object,
      default: () => ({})
    },
    provinceOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        id: null,
        vppName: "",
        province: null,
        vppType: null,
        vppTypeArray: [], // 虚拟电厂类型数组（用于多选）
        description: "",
        operatorcode: "",
        imageUrl: "", // 图片路径
        demandResponseFilingPriceCaps: null,
        demandResponseDeclaredPriceCaps: null,
        peakingDeclaredPriceFloor: null,
        peakingFilingPriceFloor: null,
        pmDeclaredPriceFloor: null,
        pmFilingPriceFloor: null,
        adjustDeclaredPriceFloor: null,
        adjustFilingPriceFloor: null,
        energyResponseDeclaredPriceFloor: null,
        energyResponseFilingPriceFloor: null,
        createTime: null
      },
      rules: {
        vppName: [
          { required: true, message: "请输入虚拟电厂名称", trigger: "blur" }
        ],
        province: [
          { required: true, message: "请选择所属省份", trigger: "change" }
        ],
        vppTypeArray: [
          {
            required: true,
            message: "请选择场站类型",
            trigger: "change",
            type: "array"
          }
        ],
        createTime: [
          { required: true, message: "请选择成立日期", trigger: "change" }
        ],
        operatorcode: [
          { required: true, message: "请输入运营商编号", trigger: "blur" }
        ]
      },
      vppTypeOptions: [], // 虚拟电厂类型选项
      // 默认图片
      defaultImage:
        "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik04MCA2MEg0MFY5MEg4MFY2MFoiIGZpbGw9IiNEREREREQiLz4KPHA+dGggZD0iTTE2MCA2MEgxMjBWOTBIMTYwVjYwWiIgZmlsbD0iI0RERERERCIvPgo8cGF0aCBkPSJNMTAwIDMwSDEwMFY2MEgxMDBWMzBaIiBzdHJva2U9IiNEREREREQiIHN0cm9rZS13aWR0aD0iMiIvPgo8dGV4dCB4PSIxMDAiIHk9IjEyMCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iIzk5OTk5OSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjEyIj7ooZXmi6XnlLXljoI8L3RleHQ+Cjwvc3ZnPgo="
    };
  },
  watch: {
    visible: {
      async handler(val) {
        this.dialogVisible = val;
        if (val) {
          if (this.vppData && this.vppData.id) {
            // 编辑模式
            // 1. 首先异步加载可用的电厂类型
            if (this.vppData.province) {
              await this.loadVppTypes(this.vppData.province);
            }

            // 2. 加载完成后，再设置整个表单的数据，确保el-select有options可以匹配
            this.form = {
              id: this.vppData.id,
              vppName: this.vppData.vppName || "",
              province: this.vppData.province || null,
              vppType: this.vppData.vppType || null,
              vppTypeArray: this.vppData.vppType
                ? this.vppData.vppType
                    .toString()
                    .split(",")
                    .map(t => parseInt(t.trim(), 10))
                    .filter(n => !isNaN(n))
                : [],
              description: this.vppData.description || "",
              operatorcode: this.vppData.operatorcode || "",
              imageUrl: this.vppData.picturePath || "",
              demandResponseFilingPriceCaps:
                this.vppData.demandResponseFilingPriceCaps || null,
              demandResponseDeclaredPriceCaps:
                this.vppData.demandResponseDeclaredPriceCaps || null,
              peakingDeclaredPriceFloor:
                this.vppData.peakingDeclaredPriceFloor || null,
              peakingFilingPriceFloor:
                this.vppData.peakingFilingPriceFloor || null,
              pmDeclaredPriceFloor: this.vppData.pmDeclaredPriceFloor || null,
              pmFilingPriceFloor: this.vppData.pmFilingPriceFloor || null,
              adjustDeclaredPriceFloor:
                this.vppData.adjustDeclaredPriceFloor || null,
              adjustFilingPriceFloor:
                this.vppData.adjustFilingPriceFloor || null,
              energyResponseDeclaredPriceFloor:
                this.vppData.energyResponseDeclaredPriceFloor || null,
              energyResponseFilingPriceFloor:
                this.vppData.energyResponseFilingPriceFloor || null,
              createTime: this.vppData.createTime || null
            };
            console.log(
              this.vppData.vppType,
              this.form.vppTypeArray,
              "2321313"
            );
          } else {
            // 新增模式：重置表单
            this.$refs.form.resetFields();
            this.form = {
              id: null,
              vppName: "",
              province: null,
              vppType: null,
              vppTypeArray: [],
              description: "",
              operatorcode: "",
              imageUrl: "",
              demandResponseFilingPriceCaps: null,
              demandResponseDeclaredPriceCaps: null,
              peakingDeclaredPriceFloor: null,
              peakingFilingPriceFloor: null,
              pmDeclaredPriceFloor: null,
              pmFilingPriceFloor: null,
              adjustDeclaredPriceFloor: null,
              adjustFilingPriceFloor: null,
              energyResponseDeclaredPriceFloor: null,
              energyResponseFilingPriceFloor: null,
              createTime: null
            };
            this.vppTypeOptions = [];
          }
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      this.$emit("update:visible", val);
    }
  },
  mounted() {
    // 初始加载时，如果已有省份，则加载对应的电厂类型
    if (this.form.province) {
      this.loadVppTypes(this.form.province);
    }
  },
  methods: {
    // 省份变化
    async handleProvinceChange(newProvinceCode) {
      // 清空已选中的电厂类型
      this.form.vppTypeArray = [];
      this.form.vppType = null;
      if (newProvinceCode) {
        this.loadVppTypes(newProvinceCode);
      } else {
        this.vppTypeOptions = [];
      }
    },
    // 根据省份加载电厂类型
    async loadVppTypes(provinceCode) {
      try {
        const response = await getPowerplantTypesByProvince(provinceCode);
        if (response.code === 0) {
          // 获取VPP_TYPE枚举映射
          const enumOptions = getEnumOptions("VPP_TYPE");
          const enumMap = {};
          enumOptions.forEach(option => {
            enumMap[option.value] = option.label;
          });

          // 将API返回的数据转换为el-option所需的格式
          this.vppTypeOptions = response.data.map(item => ({
            value: item.power_plant_type,
            label: enumMap[item.power_plant_type] || item.power_plant_type
          }));
        } else {
          this.$message.error(response.msg || "获取电厂类型失败");
          this.vppTypeOptions = [];
        }
      } catch (error) {
        console.error("获取电厂类型失败:", error);
        this.$message.error("获取电厂类型失败，请稍后重试");
        this.vppTypeOptions = [];
      }
    },
    // 处理虚拟电厂类型变化
    handleVppTypeChange(selectedTypes) {
      // 将数字数组转换为逗号分隔的字符串
      this.form.vppType = selectedTypes.join(",");
      console.log(
        "选择的类型:",
        selectedTypes,
        "转换后的vppType:",
        this.form.vppType
      );
    },

    handleClose() {
      this.dialogVisible = false;
    },
    handleConfirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 提交时转换字段名和时间格式
          const formData = {
            ...this.form,
            picturePath: this.form.imageUrl, // 将imageUrl映射为picturePath
            createTime: this.form.createTime
              ? new Date(this.form.createTime).getTime()
              : null
          };
          // 移除imageUrl字段
          delete formData.imageUrl;

          this.$emit("confirm", formData);
          this.handleClose();
        }
      });
    },

    // 图片上传前处理
    async handleImageUpload(file) {
      const isValidType = ["image/png", "image/jpg", "image/jpeg"].includes(
        file.type
      );
      const isValidSize = file.size / 1024 / 1024 < 2; // 增加到2MB

      if (!isValidType) {
        this.$message.error(this.$T("只能上传PNG、JPG、JPEG格式的图片"));
        return false;
      }
      if (!isValidSize) {
        this.$message.error(this.$T("图片大小不能超过2MB"));
        return false;
      }

      try {
        // 调用上传接口
        const response = await uploadImage(file);

        if (response.code === 0) {
          // 保存图片路径，使用API返回的storedFileName字段
          this.form.imageUrl = response.data.storedFileName;
          this.$message.success(this.$T("图片上传成功"));
        } else {
          this.$message.error(response.msg || this.$T("图片上传失败"));
        }
      } catch (error) {
        console.error("图片上传失败:", error);
        this.$message.error(this.$T("图片上传失败，请稍后重试"));
      }

      return false; // 阻止自动上传
    },

    // 删除图片
    removeImage() {
      this.form.imageUrl = "";
      this.$message.success(this.$T("图片删除成功"));
    }
  }
};
</script>

<style scoped lang="scss">
.config-dialog {
  :deep(.el-dialog__body) {
    max-height: 600px;
  }

  .dialog-content {
    max-height: 576px;
    overflow-y: auto;
  }
}
h4 {
  margin: var(--J4) 0 var(--J3) 0;
  font-size: var(--H3);
  font-weight: bold;
}
.upload-tip {
  color: var(--T4);
  font-size: var(--Ab);
  margin: 0;
}
.custom-input {
  width: 256px;
}

.price-input {
  width: 100%;
}

/* 图片上传样式 */
.image-uploader {
  display: inline-block;
}

.upload-placeholder {
  width: 120px;
  height: 120px;
  border: 2px dashed var(--BG3);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-placeholder:hover {
  border-color: var(--ZS);
}

.upload-placeholder i {
  font-size: 24px;
  color: var(--T3);
}

.image-preview {
  position: relative;
  width: 200px;
  height: 150px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
}

.image-actions {
  position: absolute;
  j
  // top: 8px;
  // right: 8px;
  // display: flex;
  // gap: 8px;
}

.image-actions i {
  width: 24px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
}

.image-actions i:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: var(--T3);
}

.station-desc-input {
  :deep(.el-textarea__inner) {
    background-color: var(--BG);
  }
}
</style>
